@extends('layouts.admin')

@section('title', 'Reservations Calendar - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Calendar</li>
                </ol>
            </nav>
        </div>
    </div>

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Calendar Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Reservations Calendar</div>
                    <div class="d-flex gap-2 align-items-center">
                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('reservations.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Legend -->
                    <div class="calendar-legend mb-4 p-3 bg-light rounded">
                        <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
                            <div>
                                <h6 class="fw-semibold mb-2">Legend:</h6>
                                <div class="d-flex flex-wrap gap-3 fs-12">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2">&nbsp;</span>
                                        <span>Pending</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-secondary me-2">&nbsp;</span>
                                        <span>Confirmed</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge" style="background-color: #8c9097;">&nbsp;</span>
                                        <span class="ms-2">Completed (Past Reservations)</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <h6 class="fw-semibold mb-2">Filter by field:</h6>
                                <select id="fieldFilter" class="form-select form-select-sm fs-12" style="min-width: 150px;">
                                    <option value="">All Fields</option>
                                    @foreach ($fields as $field)
                                        <option value="{{ $field->id }}">{{ $field->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="calendarLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading calendar...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading calendar events...</p>
                    </div>

                    <!-- Calendar Container -->
                    <div id="calendar" style="display: none;"></div>

                    <!-- Error State -->
                    <div id="calendarError" class="alert alert-danger d-none">
                        <h6 class="fw-semibold">Calendar Error</h6>
                        <p class="mb-0">Failed to load calendar events. Please refresh the page or contact support.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Reservation Details Modal Component -->
    <x-reservation-details-modal />

    <!-- Time Restriction Information Modal -->
    <x-confirmation-modal
        modal-id="timeRestrictionModal"
        type="info"
        title="Reservations are only available between 08:00 and 23:00 (opening hours)"
        warning-text="Please select a time slot within our operating hours."
        dismiss-text="OK"
        form-action="#"
    />

    <!-- Reservation Modal JavaScript -->
    <script src="{{ asset('assets/js/reservation-modal.js') }}"></script>
    @push('scripts')
        <!-- FullCalendar: using local assets from layout (avoid duplicating CDN) -->
        <!-- Locale for DD/MM/YYYY format -->
        <script src="{{ asset('assets/libs/fullcalendar/locales/en-gb.js') }}"></script>
    @endpush


    <!-- Custom FullCalendar Dark Mode Styles -->
    <style>
        /* FullCalendar Dark Mode Weekday Header Fix */
        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell .fc-col-header-cell-cushion {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Additional dark mode calendar styling for consistency */
        [data-theme-mode="dark"] .fc-theme-standard .fc-scrollgrid {
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard td,
        [data-theme-mode="dark"] .fc-theme-standard th {
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day-number {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day.fc-day-today {
            background-color: rgba(var(--primary-rgb), 0.1) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary {
            background-color: rgb(var(--primary-rgb)) !important;
            border-color: rgb(var(--primary-rgb)) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary:hover {
            background-color: rgba(var(--primary-rgb), 0.9) !important;
            border-color: rgba(var(--primary-rgb), 0.9) !important;
        }

        [data-theme-mode="dark"] .fc .fc-toolbar-title {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* SPECIFIC FIX: Target .fc-scrollgrid-section-sticky class causing white background */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            background-image: none !important;
            background: rgb(var(--body-bg-rgb2)) !important;
        }

        /* Additional targeting for sticky header elements */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky .fc-col-header-cell,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky th,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky td {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Legend dark mode styling */
        [data-theme-mode="dark"] .calendar-legend {
            background-color: rgb(var(--light-rgb)) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Ensure legend text is visible in dark mode */
        [data-theme-mode="dark"] .calendar-legend h6,
        [data-theme-mode="dark"] .calendar-legend span {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Business Hours Styling - Light Grey Background */
        .fc-timegrid-slot[data-time] {
            background-color: #f8f9fa !important; /* Very light grey for business hours */
        }

        /* Business hours styling for day grid (month view) */
        .fc-daygrid-day {
            background-color: #f8f9fa !important; /* Very light grey for business hours */
        }

        /* Non-business hours styling - slightly darker to contrast */
        .fc-timegrid-slot[data-time^="00:"],
        .fc-timegrid-slot[data-time^="01:"],
        .fc-timegrid-slot[data-time^="02:"],
        .fc-timegrid-slot[data-time^="03:"],
        .fc-timegrid-slot[data-time^="04:"],
        .fc-timegrid-slot[data-time^="05:"],
        .fc-timegrid-slot[data-time^="06:"],
        .fc-timegrid-slot[data-time^="07:"],
        .fc-timegrid-slot[data-time^="23:"] {
            background-color: #ffffff !important; /* White for non-business hours */
        }

        /* Dark mode business hours styling */
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time] {
            background-color: rgba(255, 255, 255, 0.03) !important; /* Very subtle light overlay for dark mode */
        }

        [data-theme-mode="dark"] .fc-daygrid-day {
            background-color: rgba(255, 255, 255, 0.03) !important; /* Very subtle light overlay for dark mode */
        }

        /* Dark mode non-business hours - slightly darker */
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="00:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="01:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="02:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="03:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="04:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="05:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="06:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="07:"],
        [data-theme-mode="dark"] .fc-timegrid-slot[data-time^="23:"] {
            background-color: rgb(var(--body-bg-rgb2)) !important; /* Default dark background for non-business hours */
        }

        /* Hover effect for reservations */
        .fc-event:hover {
            opacity: 0.9;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const fieldFilter = document.getElementById('fieldFilter');

            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                // Use British English locale for DD/MM/YYYY date format
                locale: 'en-gb',
                nowIndicator: true,
                businessHours: {
                    daysOfWeek: [ 0, 1, 2, 3, 4, 5, 6 ],
                    startTime: '08:00',
                    endTime: '23:00'
                },
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                timeZone: 'local',
                eventDisplay: 'block',
                displayEventTime: true,
                weekNumbers: true,
                weekNumberCalculation: 'ISO',
                navLinks: true,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                editable: false,
                selectable: true,
                events: function(fetchInfo, successCallback, failureCallback) {
                    const fieldId = fieldFilter.value;
                    const url = new URL('{{ route('calendar.events') }}');
                    url.searchParams.append('start', fetchInfo.startStr);
                    url.searchParams.append('end', fetchInfo.endStr);
                    if (fieldId) {
                        url.searchParams.append('field_id', fieldId);
                    }

                    console.log('Fetching calendar events from:', url.toString());

                    fetch(url)
                        .then(response => {
                            console.log('Calendar events response status:',
                                response.status);
                            if (!response.ok) {
                                throw new Error(
                                    `HTTP error! status: ${response.status}`
                                );
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar events data received:', data);
                            document.getElementById('calendarLoading').style
                                .display = 'none';
                            document.getElementById('calendar').style.display =
                                'block';
                            document.getElementById('calendarError').classList
                                .add('d-none');
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Calendar events fetch error:',
                                error);
                            document.getElementById('calendarLoading').style
                                .display = 'none';
                            document.getElementById('calendarError').classList
                                .remove('d-none');
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    // Prevent default navigation
                    info.jsEvent.preventDefault();

                    // Open reservation details in modal
                    openReservationModal(info.event.extendedProps.reservation_id);
                },
                dateClick: function(info) {
                    // Get the current view type
                    const currentView = calendar.view.type;

                    // Month view behavior: navigate to day view for the clicked date
                    if (currentView === 'dayGridMonth') {
                        // Switch to day view for the clicked date
                        calendar.changeView('timeGridDay', info.dateStr);
                    }
                    // Week/Day view behavior: open reservation creation screen
                    else if (currentView === 'timeGridWeek' || currentView === 'timeGridDay') {
                        // Check if time is included (time grid views)
                        if (info.dateStr.includes('T')) {
                            const [date, time] = info.dateStr.split('T');

                            // Validate time slot is within operating hours (08:00 - 23:00)
                            if (!isWithinOperatingHours(time)) {
                                // Show time restriction modal
                                showTimeRestrictionModal();
                                return;
                            }

                            // Time is valid, proceed with reservation creation
                            const url = `/reservations/create?date=${encodeURIComponent(date)}&time=${encodeURIComponent(time)}`;
                            window.location.href = url;
                        } else {
                            // No specific time, redirect to reservation creation page with date only
                            const url = '/reservations/create?date=' + encodeURIComponent(info.dateStr);
                            window.location.href = url;
                        }
                    }
                },
                eventDidMount: function(info) {
                    // Add tooltip with reservation details
                    const props = info.event.extendedProps;
                    const pastIndicator = props.is_past ? '\n(Past Reservation)' : '';
                    info.el.title =
                        `${props.field_name}\nCustomer: ${props.customer_name}\nStatus: ${props.status}\nCost: XCG ${props.total_cost}\nDuration: ${props.duration} hours${pastIndicator}`;

                    // Add CSS class for past reservations
                    if (props.is_past) {
                        info.el.classList.add('past-reservation');
                    }
                },
                eventDrop: function(info) {
                    const event = info.event;

                    fetch(`/calendar/update-reservation/${event.id}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content')
                            },
                            body: JSON.stringify({
                                start: event.startStr,
                                end: event.endStr || null
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Failed to update reservation.');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log(data.message);
                        })
                        .catch(error => {
                            alert('Error updating reservation.');
                            info.revert(); // undo the drag
                        });
                }
            });

            // Show loading state initially
            document.getElementById('calendarLoading').style.display = 'block';
            document.getElementById(
                'calendar').style.display = 'none';

            calendar.render();

            // Initial load complete
            setTimeout(() => {
                document.getElementById('calendarLoading').style.display = 'none';
                document.getElementById('calendar').style.display = 'block';
            }, 1000);

            // Refresh calendar when field filter changes
            fieldFilter.addEventListener('change', function() {
                document.getElementById('calendarLoading').style.display = 'block';
                document.getElementById('calendar').style.display = 'none';
                calendar.refetchEvents();
            });
        });

        /**
         * Check if a time is within operating hours (08:00 - 23:00)
         * @param {string} timeStr - Time string in HH:MM format
         * @returns {boolean} - True if within operating hours
         */
        function isWithinOperatingHours(timeStr) {
            // Parse the time string (format: HH:MM or HH:MM:SS)
            const timeParts = timeStr.split(':');
            const hour = parseInt(timeParts[0], 10);
            const minute = parseInt(timeParts[1], 10);

            // Convert to minutes for easier comparison
            const timeInMinutes = hour * 60 + minute;
            const openingTime = 8 * 60; // 08:00 in minutes
            const closingTime = 23 * 60; // 23:00 in minutes

            // Check if time is within operating hours
            return timeInMinutes >= openingTime && timeInMinutes < closingTime;
        }

        /**
         * Show the time restriction information modal
         */
        function showTimeRestrictionModal() {
            const modal = new bootstrap.Modal(document.getElementById('timeRestrictionModal'));
            modal.show();
        }
    </script>
@endsection
